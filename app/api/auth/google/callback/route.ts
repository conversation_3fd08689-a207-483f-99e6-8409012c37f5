import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // <PERSON><PERSON><PERSON> tra nếu có token trực tiếp từ backend (nếu backend redirect với params)
    const accessToken = searchParams.get('access_token');
    const userInfo = searchParams.get('user_info');

    if (accessToken) {
      // Backend đã redirect với token, xử lý trực tiếp
      const redirectResponse = NextResponse.redirect(new URL('/admin', request.url));

      // Set cookies với token
      redirectResponse.cookies.set('access_token', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/',
      });

      redirectResponse.cookies.set('client_access_token', accessToken, {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/',
      });

      if (userInfo) {
        redirectResponse.cookies.set('user_info', userInfo, {
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 60 * 60 * 24 * 7, // 7 days
          path: '/',
        });
      }

      return redirectResponse;
    }

    // Nếu không có token, xử lý theo cách cũ với authorization code
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    if (error) {
      console.error('Google OAuth error:', error);
      return NextResponse.redirect(new URL('/login?error=oauth_error', request.url));
    }

    if (!code) {
      console.error('No authorization code received');
      return NextResponse.redirect(new URL('/login?error=no_code', request.url));
    }

    // Chuyển tiếp request đến backend
    const backendCallbackUrl = new URL('/auth/google/callback', 'http://localhost:8001');
    backendCallbackUrl.searchParams.set('code', code);
    if (state) {
      backendCallbackUrl.searchParams.set('state', state);
    }

    const response = await fetch(backendCallbackUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Backend callback failed:', response.status);
      return NextResponse.redirect(new URL('/login?error=backend_error', request.url));
    }

    const data = await response.json();
    
    // Tạo response redirect về trang chính
    const redirectResponse = NextResponse.redirect(new URL('/admin', request.url));
    
    // Set cookies với token
    if (data.access_token) {
      // Set HTTP-only cookie cho server-side
      redirectResponse.cookies.set('access_token', data.access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/',
      });

      // Set client-accessible cookie
      redirectResponse.cookies.set('client_access_token', data.access_token, {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/',
      });
    }

    // Set user info cookie nếu có
    if (data.user_info) {
      redirectResponse.cookies.set('user_info', JSON.stringify(data.user_info), {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/',
      });
    }

    return redirectResponse;

  } catch (error) {
    console.error('Google callback error:', error);
    return NextResponse.redirect(new URL('/login?error=callback_error', request.url));
  }
}
