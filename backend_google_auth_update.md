# C<PERSON><PERSON> nhật Backend cho Google OAuth

<PERSON> login với Google hoạt động đúng và tự redirect về trang ch<PERSON>, b<PERSON><PERSON> c<PERSON><PERSON> cậ<PERSON> nhật backend như sau:

## 1. <PERSON><PERSON><PERSON> nhật endpoint `/auth/google`

```python
@app.get('/auth/google')
async def login_via_google(request: Request):
    # Thêm redirect_uri chính xác về frontend callback
    redirect_uri = "http://localhost:3000/api/auth/google/callback"
    return await oauth.google.authorize_redirect(request, redirect_uri)
```

## 2. Cập nhật endpoint `/auth/google/callback`

```python
from fastapi.responses import RedirectResponse

@app.get('/auth/google/callback')
async def auth_google_callback(request: Request):
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = await oauth.google.parse_id_token(request, token)

        # Tạo JWT token hoặc xử lý user như bình thường
        # ... logic xử lý user_info ...

        # Thay vì trả JSON, redirect về frontend với token trong URL
        frontend_url = f"http://localhost:3000/api/auth/google/callback"
        
        # Thêm token vào query params để frontend xử lý
        params = {
            'access_token': token['access_token'],
            'id_token': token.get('id_token', ''),
            'user_info': json.dumps(user_info)
        }
        
        # Tạo URL với params
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        redirect_url = f"{frontend_url}?{query_string}"
        
        return RedirectResponse(url=redirect_url)
        
    except Exception as e:
        # Redirect về login với error
        return RedirectResponse(url="http://localhost:3000/login?error=oauth_error")
```

## 3. Hoặc cách đơn giản hơn (khuyến nghị)

Nếu bạn muốn đơn giản hơn, có thể để backend trả JSON như hiện tại và cập nhật frontend callback:

```python
@app.get('/auth/google/callback')
async def auth_google_callback(request: Request):
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = await oauth.google.parse_id_token(request, token)

        # Xử lý user_info, tạo JWT token nếu cần
        # ... logic xử lý ...

        # Trả JSON như hiện tại
        return JSONResponse(content={
            "access_token": token['access_token'],
            "id_token": token.get('id_token'),
            "user_info": user_info,
            "success": True
        })
        
    except Exception as e:
        return JSONResponse(
            content={"error": str(e), "success": False}, 
            status_code=400
        )
```

## Lưu ý quan trọng:

1. **CORS**: Đảm bảo backend có cấu hình CORS cho phép frontend gọi API
2. **Redirect URI**: Trong Google Console, thêm `http://localhost:3000/api/auth/google/callback` vào danh sách redirect URIs
3. **Environment**: Cập nhật URL cho production environment

## Kiểm tra Google Console

Đảm bảo trong Google Cloud Console > APIs & Services > Credentials:
- Authorized redirect URIs có: `http://localhost:3000/api/auth/google/callback`
- Authorized JavaScript origins có: `http://localhost:3000`
